# SAiWELL Flutter Project Setup Guide

## Project Overview
**Project Name:** SAiWELL  
**Version:** 3.6.7+2  
**Platform:** Flutter (iOS, Android, Web, Desktop)  
**Firebase Project:** sfoto-clinic-396605

## Prerequisites

### 1. Development Environment
- **macOS:** 15.5 24F74 darwin-arm64 (tested environment)
- **Xcode:** 16.1 (Build 16B40) - for iOS development
- **Android Studio:** 2025.1 - for Android development
- **VS Code:** 1.101.1 (recommended IDE)

### 2. Required SDKs & Tools
- **Flutter SDK:** 3.29.2 (Channel stable) - **EXACT VERSION REQUIRED**
- **Dart SDK:** 3.7.2 (included with Flutter)
- **Java:** OpenJDK 21.0.6 (build 21.0.6+-********-b895.109) - **EXACT VERSION REQUIRED**
- **CocoaPods:** 1.16.2 (for iOS dependencies)
- **Git:** For version control

### 3. Platform-Specific Requirements

#### iOS Development
- **iOS Deployment Target:** 15.5+
- **Xcode Command Line Tools:** Included with Xcode 16.1
- **Apple Developer Account** (for device testing/deployment)

#### Android Development
- **Android SDK:** API Level 35 (compile), API Level 26 (minimum)
- **Android SDK Location:** `/Users/<USER>/Library/Android/sdk`
- **Android NDK:** 27.2.********
- **Build Tools:** 35.0.0 (current version)
- **Platform Tools:** android-35

## Installation Steps

### Step 1: Install Flutter (EXACT VERSION)
```bash
# Download Flutter SDK 3.29.2 from https://flutter.dev/docs/get-started/install/macos
# Extract to ~/flutter (recommended location)
export PATH="$PATH:$HOME/flutter/bin"

# Add to your shell profile (~/.zshrc or ~/.bash_profile)
echo 'export PATH="$PATH:$HOME/flutter/bin"' >> ~/.zshrc

# Verify installation - MUST show Flutter 3.29.2
flutter --version
flutter doctor
```

### Step 2: Install Development Tools (EXACT VERSIONS)
```bash
# Install Xcode 16.1 from App Store
# Install Android Studio 2025.1 from https://developer.android.com/studio

# Install CocoaPods 1.16.2
sudo gem install cocoapods

# Install VS Code 1.101.1 and Flutter extension 3.116.0
code --install-extension Dart-Code.flutter

# Verify versions
xcodebuild -version
pod --version
```

### Step 3: Configure Android Environment (EXACT PATHS)
```bash
# Set Android SDK path in ~/.zshrc (for macOS 15.5+)
export ANDROID_HOME=$HOME/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/tools/bin
export PATH=$PATH:$ANDROID_HOME/platform-tools

# Reload shell configuration
source ~/.zshrc

# Accept Android licenses (REQUIRED)
flutter doctor --android-licenses

# Verify Android setup
flutter doctor
```

### Step 4: Clone and Setup Project
```bash
# Clone the repository
git clone [REPOSITORY_URL]
cd FLUTTER_SAiWELL

# Get Flutter dependencies
flutter pub get

# Generate launcher icons
flutter pub run flutter_launcher_icons:main

# Generate native splash screens
flutter pub run flutter_native_splash:create
```

## Required Configuration Files

### 1. Android Signing Configuration
Create `android/key.properties` with:
```properties
storePassword=Saiwell@2024
keyPassword=Saiwell@2024
keyAlias=saiwell_flutter
storeFile=saiwell.jks
```
**Note:** Obtain the `saiwell.jks` keystore file from the project maintainer.

### 2. Android Local Properties
Update `android/local.properties` with EXACT paths:
```properties
sdk.dir=/Users/<USER>/Library/Android/sdk
flutter.sdk=/Users/<USER>/flutter
flutter.buildMode=release
flutter.versionName=3.6.7
flutter.versionCode=2
```
**Note:** Replace `[YOUR_USERNAME]` with your actual macOS username.

### 3. Firebase Configuration
The project includes Firebase configuration files:
- `android/app/google-services.json` (Android)
- `ios/Runner/GoogleService-Info.plist` (iOS)
- `lib/firebase_options.dart` (Dart configuration)

**Note:** These files are already configured for the project.

### 4. Google Cloud Credentials
Ensure `assets/gCloud/credentials.json` exists with proper Google Cloud service account credentials.

## Special Dependencies & Native Libraries

### Android Native Libraries
- **Ring SDK:** `2301sdk5.0.jar` (located in `android/app/libs/`)
- **Google Play Services:** Fitness, GCM
- **Firebase:** Analytics, Crashlytics, Messaging, Firestore
- **Health Connect:** For health data integration

### iOS Native Libraries
- **BLE SDK:** Custom Bluetooth Low Energy SDK
- **YCProductSDK.framework:** Custom framework
- **ARKit:** For augmented reality features
- **HealthKit:** For health data integration

### Key Flutter Packages
- **Firebase Suite:** Core, Auth, Firestore, Analytics, Crashlytics
- **Health Integration:** health package for HealthKit/Health Connect
- **BLE Communication:** flutter_reactive_ble
- **Camera & ML:** camera, google_mlkit_face_detection, arkit_plugin
- **Audio Recording:** record, audio_waveforms
- **Charts:** syncfusion_flutter_charts

## Environment Verification

### Verify Your Setup Matches Project Requirements
Run these commands to ensure your environment matches the tested configuration:

```bash
# Check Flutter version (MUST be 3.29.2)
flutter --version

# Check Dart version (MUST be 3.7.2)
dart --version

# Check Java version (MUST be OpenJDK 21.0.6)
java -version

# Check Xcode version (MUST be 16.1)
xcodebuild -version

# Check CocoaPods version (MUST be 1.16.2)
pod --version

# Check Android SDK setup
flutter doctor -v

# Expected flutter doctor output should show:
# ✓ Flutter (Channel stable, 3.29.2, on macOS 15.5 24F74 darwin-arm64)
# ✓ Android toolchain - develop for Android devices (Android SDK version 35.0.0)
# ✓ Xcode - develop for iOS and macOS (Xcode 16.1)
# ✓ Chrome - develop for the web
# ✓ Android Studio (version 2025.1)
# ✓ VS Code (version 1.101.1)
```

**⚠️ IMPORTANT:** If any versions don't match, install the exact versions listed above.

## Build Instructions

### Development Build
```bash
# iOS
flutter run -d ios

# Android
flutter run -d android

# Web
flutter run -d chrome
```

### Release Build
```bash
# iOS Release
flutter build ios --release

# Android Release APK
flutter build apk --release

# Android Release Bundle
flutter build appbundle --release
```

## Platform-Specific Setup

### iOS Setup
```bash
cd ios
pod install
cd ..
```

**Important iOS Notes:**
- Development Team ID: `J5HR7RXU3W`
- Bundle Identifier: `com.saigeware.sh`
- Requires iOS 15.5+ deployment target
- Custom BLE SDK integration requires specific entitlements

### Android Setup
```bash
# Ensure Android SDK and NDK are properly installed
flutter doctor --android-licenses
```

**Important Android Notes:**
- Application ID: `com.saiwell.sw`
- Minimum SDK: 26, Target SDK: 35
- Requires Java 21 for compilation
- Custom Ring SDK integration

## Permissions Required

### iOS Permissions
- Camera
- Microphone
- Location (Always/When in Use)
- Bluetooth
- Health Data
- Notifications
- Photo Library
- Activity Recognition

### Android Permissions
- Camera
- Microphone
- Location (Fine/Coarse)
- Bluetooth (Connect/Advertise)
- Health Data Access
- Notifications
- Storage Access
- Activity Recognition

## Troubleshooting

### Common Issues & Solutions
1. **Flutter Doctor Issues:**
   - Run `flutter doctor --android-licenses` to accept Android licenses
   - Ensure exact versions: Flutter 3.29.2, Java 21.0.6, Xcode 16.1
2. **iOS Build Failures:**
   - Clean build folder: `flutter clean`
   - Reinstall pods: `cd ios && pod install && cd ..`
   - Check Xcode version is exactly 16.1
3. **Android Build Failures:**
   - Verify Java version is OpenJDK 21.0.6
   - Check Android SDK path: `/Users/<USER>/Library/Android/sdk`
   - Run `flutter doctor --android-licenses`
4. **Missing Dependencies:**
   - Run `flutter pub get` and `flutter clean`
   - Ensure all native libraries are present in `android/app/libs/` and `ios/Runner/`

### Build Clean Commands
```bash
flutter clean
flutter pub get
cd ios && pod install && cd ..
flutter build [platform] --release
```

## Environment Variables
No special environment variables required beyond standard Flutter/Android/iOS development setup.

## Testing
```bash
# Run unit tests
flutter test

# Run integration tests
flutter test integration_test/
```

## Deployment Notes
- **iOS:** Requires Apple Developer account and proper provisioning profiles
- **Android:** Uses release signing configuration from `key.properties`
- **Firebase:** Pre-configured for production environment

## Support & Maintenance
- Ensure all team members have access to required certificates and keys
- Keep Firebase configuration files secure and up-to-date
- Regular dependency updates should be tested thoroughly due to native integrations

---

**Last Updated:** August 2025  
**Project Version:** 3.6.7+2
