# SAiWELL Flutter Project Setup Guide

## Project Overview
**Project Name:** SAiWELL  
**Version:** 3.6.7+2  
**Platform:** Flutter (iOS, Android, Web, Desktop)  
**Firebase Project:** sfoto-clinic-396605

## Prerequisites

### 1. Development Environment
- **macOS:** Latest version recommended
- **Xcode:** 15.0+ (for iOS development)
- **Android Studio:** Latest version
- **VS Code:** Latest version (recommended IDE)

### 2. Required SDKs & Tools
- **Flutter SDK:** 3.0.0 or higher
- **Dart SDK:** Included with Flutter
- **Java:** JDK 21 (required for Android build)
- **CocoaPods:** Latest version (for iOS dependencies)
- **Git:** For version control

### 3. Platform-Specific Requirements

#### iOS Development
- **iOS Deployment Target:** 15.5+
- **Xcode Command Line Tools**
- **Apple Developer Account** (for device testing/deployment)

#### Android Development
- **Android SDK:** API Level 35 (compile), API Level 26 (minimum)
- **Android NDK:** 27.2.********
- **Build Tools:** 34.0.0

## Installation Steps

### Step 1: Install Flutter
```bash
# Download Flutter SDK from https://flutter.dev/docs/get-started/install/macos
# Extract to desired location (e.g., ~/flutter)
export PATH="$PATH:[PATH_TO_FLUTTER_GIT_DIRECTORY]/flutter/bin"

# Verify installation
flutter doctor
```

### Step 2: Install Development Tools
```bash
# Install Xcode from App Store
# Install Android Studio from https://developer.android.com/studio

# Install CocoaPods
sudo gem install cocoapods

# Install VS Code Flutter extension
code --install-extension Dart-Code.flutter
```

### Step 3: Configure Android Environment
```bash
# Set Android SDK path in ~/.bash_profile or ~/.zshrc
export ANDROID_HOME=$HOME/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/tools/bin
export PATH=$PATH:$ANDROID_HOME/platform-tools
```

### Step 4: Clone and Setup Project
```bash
# Clone the repository
git clone [REPOSITORY_URL]
cd FLUTTER_SAiWELL

# Get Flutter dependencies
flutter pub get

# Generate launcher icons
flutter pub run flutter_launcher_icons:main

# Generate native splash screens
flutter pub run flutter_native_splash:create
```

## Required Configuration Files

### 1. Android Signing Configuration
Create `android/key.properties` with:
```properties
storePassword=Saiwell@2024
keyPassword=Saiwell@2024
keyAlias=saiwell_flutter
storeFile=saiwell.jks
```
**Note:** Obtain the `saiwell.jks` keystore file from the project maintainer.

### 2. Android Local Properties
Update `android/local.properties`:
```properties
sdk.dir=/Users/<USER>/Library/Android/sdk
flutter.sdk=/Users/<USER>/flutter
flutter.buildMode=release
flutter.versionName=3.6.7
flutter.versionCode=2
```

### 3. Firebase Configuration
The project includes Firebase configuration files:
- `android/app/google-services.json` (Android)
- `ios/Runner/GoogleService-Info.plist` (iOS)
- `lib/firebase_options.dart` (Dart configuration)

**Note:** These files are already configured for the project.

### 4. Google Cloud Credentials
Ensure `assets/gCloud/credentials.json` exists with proper Google Cloud service account credentials.

## Special Dependencies & Native Libraries

### Android Native Libraries
- **Ring SDK:** `2301sdk5.0.jar` (located in `android/app/libs/`)
- **Google Play Services:** Fitness, GCM
- **Firebase:** Analytics, Crashlytics, Messaging, Firestore
- **Health Connect:** For health data integration

### iOS Native Libraries
- **BLE SDK:** Custom Bluetooth Low Energy SDK
- **YCProductSDK.framework:** Custom framework
- **ARKit:** For augmented reality features
- **HealthKit:** For health data integration

### Key Flutter Packages
- **Firebase Suite:** Core, Auth, Firestore, Analytics, Crashlytics
- **Health Integration:** health package for HealthKit/Health Connect
- **BLE Communication:** flutter_reactive_ble
- **Camera & ML:** camera, google_mlkit_face_detection, arkit_plugin
- **Audio Recording:** record, audio_waveforms
- **Charts:** syncfusion_flutter_charts

## Build Instructions

### Development Build
```bash
# iOS
flutter run -d ios

# Android
flutter run -d android

# Web
flutter run -d chrome
```

### Release Build
```bash
# iOS Release
flutter build ios --release

# Android Release APK
flutter build apk --release

# Android Release Bundle
flutter build appbundle --release
```

## Platform-Specific Setup

### iOS Setup
```bash
cd ios
pod install
cd ..
```

**Important iOS Notes:**
- Development Team ID: `J5HR7RXU3W`
- Bundle Identifier: `com.saigeware.sh`
- Requires iOS 15.5+ deployment target
- Custom BLE SDK integration requires specific entitlements

### Android Setup
```bash
# Ensure Android SDK and NDK are properly installed
flutter doctor --android-licenses
```

**Important Android Notes:**
- Application ID: `com.saiwell.sw`
- Minimum SDK: 26, Target SDK: 35
- Requires Java 21 for compilation
- Custom Ring SDK integration

## Permissions Required

### iOS Permissions
- Camera
- Microphone
- Location (Always/When in Use)
- Bluetooth
- Health Data
- Notifications
- Photo Library
- Activity Recognition

### Android Permissions
- Camera
- Microphone
- Location (Fine/Coarse)
- Bluetooth (Connect/Advertise)
- Health Data Access
- Notifications
- Storage Access
- Activity Recognition

## Troubleshooting

### Common Issues
1. **Flutter Doctor Issues:** Run `flutter doctor` and resolve all issues
2. **iOS Build Failures:** Clean build folder and run `pod install`
3. **Android Build Failures:** Check Java version (must be JDK 21)
4. **Missing Dependencies:** Run `flutter pub get` and `flutter clean`

### Build Clean Commands
```bash
flutter clean
flutter pub get
cd ios && pod install && cd ..
flutter build [platform] --release
```

## Environment Variables
No special environment variables required beyond standard Flutter/Android/iOS development setup.

## Testing
```bash
# Run unit tests
flutter test

# Run integration tests
flutter test integration_test/
```

## Deployment Notes
- **iOS:** Requires Apple Developer account and proper provisioning profiles
- **Android:** Uses release signing configuration from `key.properties`
- **Firebase:** Pre-configured for production environment

## Support & Maintenance
- Ensure all team members have access to required certificates and keys
- Keep Firebase configuration files secure and up-to-date
- Regular dependency updates should be tested thoroughly due to native integrations

---

**Last Updated:** August 2025  
**Project Version:** 3.6.7+2
