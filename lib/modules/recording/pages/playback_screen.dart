import 'package:SAiWELL/modules/recording/controller/playback_controller.dart';
import 'package:SAiWELL/modules/recording/controller/recording_controller.dart';
import 'package:SAiWELL/modules/recording/controller/instruction_controller.dart';
import 'package:SAiWELL/utils/common_methods.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../services/audio_service.dart';
import '../../../utils/const/app_colors.dart';
import '../../../utils/reusableWidgets/build_circular_icon_button.dart';
import '../../../utils/reusableWidgets/custom_app_bar.dart';
import '../../../utils/reusableWidgets/reusable_button.dart';

class PlaybackScreen extends StatefulWidget {
  const PlaybackScreen({super.key});

  static const routeName = "/playbackScreen";

  @override
  State<PlaybackScreen> createState() => _PlaybackScreenState();
}

class _PlaybackScreenState extends State<PlaybackScreen> {
  PlaybackController playbackController = Get.find<PlaybackController>();
  RecordingController recordingController = Get.find<RecordingController>();
  InstructionController instructionController = Get.find<InstructionController>();

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool canPop, o) {
        if (!canPop) {
          recordingController.resetRecording();
          Get.back();
        }
      },
      child: Scaffold(
        appBar: CustomAppBar(
          title: "Recording",
          onBackButtonPressed: () {
            recordingController.resetRecording();
            Get.back();
          },
        ),
        body: SafeArea(
          child: Column(
            children: [
              _buildTitleSection(),
              const SizedBox(
                height: 16,
              ),
              Expanded(child: _buildPlaybackBody(context)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTitleSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(
            height: 24,
          ),
          Text(
            "Your Recording Has Been Saved",
            style: TextStyle(
              color: AppColors.primaryTeal,
              fontSize: 20,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            instructionController.isFromGtPlus
                ? "Thank you for providing your data."
                : "Thank you for providing your data. Your updated health parameters can be viewed shortly under View Health Record.",
            textAlign: TextAlign.justify,
            style: TextStyle(
              color: AppColors.mediumGray,
              height: 1.6,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaybackBody(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // ReusableButton(
          //     title: "Share .Wav Recording",
          //     width: context.width * .33,
          //     height: 40,
          //     onTap: () {
          //       playbackController.shareRecording();
          //     }
          // ),
          const Spacer(),
          Divider(
            color: Colors.black.withOpacity(.1),
          ),
          const SizedBox(height: 36),
          Padding(
            padding: const EdgeInsets.only(left: 24.0),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                "Recording",
                style: TextStyle(
                  color: AppColors.mediumGray,
                ),
              ),
            ),
          ),
          _buildSeekbar(),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 22),
            child: Column(
              children: [
                const SizedBox(height: 16),
                _buildPlaybackControls(),
                const SizedBox(height: 24),
                _buildRerecordButton(),
                const SizedBox(
                  height: 30,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildSeekbar() {
    return Obx(() {
      final double totalDurationInMs = CommonMethods.parseDurationToMs(
          recordingController.recordingDuration);
      final double currentPosition =
          playbackController.currentPosition.value.isNaN
              ? 0.0
              : playbackController.currentPosition.value;
      return SliderTheme(
        data: SliderTheme.of(context).copyWith(
          thumbShape: SliderComponentShape.noThumb,
          trackHeight: 3.0,
          activeTrackColor: AppColors.primaryOrange,
          inactiveTrackColor: AppColors.snowGray,
          trackShape: const RoundedRectSliderTrackShape(),
        ),
        child: Slider(
          value: currentPosition.clamp(0.0, totalDurationInMs),
          max: totalDurationInMs,
          onChanged: (double value) {
            //  playbackController.playerController.seekTo(value.toInt());
          },
        ),
      );
    });
  }

  Widget _buildPlaybackControls() {
    return Obx(() {
      final double currentPosition =
          playbackController.currentPosition.value.isNaN
              ? 0.0
              : playbackController.currentPosition.value;
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            CommonMethods.formatMilliSecondsToMMSS(currentPosition),
            style: TextStyle(
              color: AppColors.primaryOrange,
              fontSize: 12,
            ),
          ),
          const Spacer(),
          Obx(
            () => buildCircularIconButton(
              icon: playbackController
                          .audioRecordingService.playbackState.value ==
                      PlaybackState.playing
                  ? Icons.pause
                  : Icons.play_arrow,
              color: AppColors.primaryOrange,
              iconColor: Colors.white,
              onPressed: () {
                if (playbackController
                        .audioRecordingService.playbackState.value ==
                    PlaybackState.playing) {
                  playbackController.pausePlayback();
                } else if (playbackController
                        .audioRecordingService.playbackState.value ==
                    PlaybackState.paused) {
                  playbackController.resumePlayback();
                } else {
                  playbackController.playRecording();
                }
              },
            ),
          ),
          const Spacer(),
          Text(
            recordingController.recordingDuration,
            style: TextStyle(
              color: AppColors.lightGray,
              fontSize: 12,
            ),
          ),
        ],
      );
    });
  }

  Widget _buildRerecordButton() {
    return Row(
      children: [
        Expanded(
          child: ReusableButton(
            title: "Re-Record",
            fontColor: AppColors.primaryOrange,
            color: Colors.white,
            onTap: () {
              recordingController.resetRecording();
              Get.back();
            },
          ),
        ),
        const SizedBox(
          width: 24,
        ),
        Expanded(
          child: Obx(
            () => ReusableButton(
              title: "Done",
              isLoading: playbackController.isDataUploading.value,
              onTap: () {
                playbackController.uploadFileToStorage(
                  context: context,
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
